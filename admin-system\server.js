const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
require('dotenv').config();

// 初始化微信云开发
const cloud = require('wx-server-sdk');
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
});

const db = cloud.database();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 路由配置

// 首页
app.get('/', (req, res) => {
  res.render('index', { title: '修仙游戏后台管理系统' });
});

// 玩家管理页面
app.get('/players', (req, res) => {
  res.render('players', { title: '玩家管理' });
});

// 邮件管理页面
app.get('/mails', (req, res) => {
  res.render('mails', { title: '邮件管理' });
});

// API路由

// 获取玩家列表
app.get('/api/players', async (req, res) => {
  try {
    const { search, page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;
    
    let query = {};
    
    // 如果有搜索条件
    if (search) {
      // 尝试按openid搜索
      if (search.startsWith('o')) {
        query._openid = new RegExp(search, 'i');
      } else {
        // 按昵称搜索
        query.nickname = new RegExp(search, 'i');
      }
    }
    
    const result = await db.collection('player')
      .where(query)
      .skip(skip)
      .limit(parseInt(limit))
      .orderBy('createdAt', 'desc')
      .get();
    
    // 获取总数
    const countResult = await db.collection('player')
      .where(query)
      .count();
    
    res.json({
      success: true,
      data: result.data,
      total: countResult.total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取玩家列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取单个玩家详情
app.get('/api/players/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await db.collection('player').doc(id).get();
    
    if (!result.data) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    res.json({
      success: true,
      data: result.data
    });
  } catch (error) {
    console.error('获取玩家详情失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 发送邮件给指定玩家
app.post('/api/send-mail', async (req, res) => {
  try {
    const {
      recipients, // 接收者列表 [{openid, nickname}]
      title,
      content,
      sender = '系统管理员',
      rewards = {}
    } = req.body;
    
    if (!recipients || recipients.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Recipients are required'
      });
    }
    
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        error: 'Title and content are required'
      });
    }
    
    // 检查是否有奖励
    const hasRewards = Object.keys(rewards).some(key => {
      if (key === 'items') {
        return rewards[key] && rewards[key].length > 0;
      }
      return rewards[key] && rewards[key] > 0;
    });
    
    // 创建邮件记录
    const mailData = {
      title,
      content,
      sender,
      rewards: hasRewards ? rewards : null,
      has_rewards: hasRewards,
      created_at: db.serverDate(),
      updated_at: db.serverDate()
    };
    
    const mailResult = await db.collection('mails').add({
      data: mailData
    });
    
    const mailId = mailResult._id;
    
    // 为每个接收者创建邮件接收记录
    const recipientPromises = recipients.map(recipient => {
      return db.collection('mail_recipients').add({
        data: {
          _openid: recipient.openid,
          mail_id: mailId,
          title,
          content,
          sender,
          rewards: hasRewards ? rewards : null,
          has_rewards: hasRewards,
          is_read: false,
          is_claimed: false,
          deleted: false,
          created_at: db.serverDate(),
          updated_at: db.serverDate()
        }
      });
    });
    
    await Promise.all(recipientPromises);
    
    res.json({
      success: true,
      message: `邮件已发送给 ${recipients.length} 个玩家`,
      mailId
    });
    
  } catch (error) {
    console.error('发送邮件失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取邮件列表
app.get('/api/mails', async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;
    
    const result = await db.collection('mails')
      .skip(skip)
      .limit(parseInt(limit))
      .orderBy('created_at', 'desc')
      .get();
    
    // 获取总数
    const countResult = await db.collection('mails').count();
    
    res.json({
      success: true,
      data: result.data,
      total: countResult.total,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取邮件列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 删除邮件
app.delete('/api/mails/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 删除邮件记录
    await db.collection('mails').doc(id).remove();
    
    // 删除所有相关的接收记录
    await db.collection('mail_recipients').where({
      mail_id: id
    }).remove();
    
    res.json({
      success: true,
      message: 'Mail deleted successfully'
    });
  } catch (error) {
    console.error('删除邮件失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`修仙游戏后台管理系统运行在 http://localhost:${PORT}`);
});
