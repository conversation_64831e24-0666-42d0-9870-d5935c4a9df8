# 修仙游戏后台管理系统

这是一个基于 Node.js + Express 的后台管理系统，用于管理修仙游戏的玩家数据和邮件系统。

## 功能特性

### 1. 玩家管理
- 查看所有玩家列表
- 按 OpenID 或昵称搜索玩家
- 查看玩家详细信息（昵称、VIP等级、资源等）
- 批量选择玩家发送邮件

### 2. 邮件系统
- 创建全服邮件
- 向指定玩家发送邮件
- 支持多种奖励类型：
  - 基础资源：仙玉、灵石、历练点、剑意
  - 物品奖励：材料、消耗品、装备等
- 邮件列表管理
- 邮件详情查看
- 邮件删除功能

### 3. 系统概览
- 玩家统计数据
- 邮件统计数据
- 系统状态监控

## 安装和运行

### 1. 安装依赖
```bash
cd admin-system
npm install
```

### 2. 配置环境变量
编辑 `.env` 文件，设置正确的微信云开发环境ID：
```
WX_CLOUD_ENV=your-cloud-env-id
```

### 3. 启动服务器
```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 4. 访问系统
打开浏览器访问：`http://localhost:3000`

## 数据库结构

系统使用腾讯云数据库，主要涉及以下集合：

### player 集合
存储玩家基础信息：
- `_openid`: 玩家唯一标识
- `nickname`: 玩家昵称
- `xianyu`: 仙玉数量
- `lingshi`: 灵石数量
- `lianlidian`: 历练点数量
- `sword_intent`: 剑意数量
- `vip_level`: VIP等级
- 其他游戏数据...

### mails 集合
存储邮件模板：
- `title`: 邮件标题
- `content`: 邮件内容
- `sender`: 发送者
- `rewards`: 奖励内容
- `has_rewards`: 是否有奖励
- `created_at`: 创建时间

### mail_recipients 集合
存储玩家邮件接收记录：
- `_openid`: 接收者OpenID
- `mail_id`: 邮件ID
- `is_read`: 是否已读
- `is_claimed`: 是否已领取奖励
- `deleted`: 是否已删除
- 其他邮件数据...

### items 集合
存储玩家物品：
- `_openid`: 玩家OpenID
- `name`: 物品名称
- `type`: 物品类型
- `count`: 物品数量
- 其他物品属性...

## API 接口

### 玩家相关
- `GET /api/players` - 获取玩家列表
- `GET /api/players/:id` - 获取玩家详情

### 邮件相关
- `POST /api/send-mail` - 发送邮件
- `GET /api/mails` - 获取邮件列表
- `DELETE /api/mails/:id` - 删除邮件

## 使用说明

### 发送邮件流程
1. 在玩家管理页面选择目标玩家
2. 点击"发送邮件"按钮
3. 填写邮件标题、内容和奖励
4. 确认发送

### 创建全服邮件
1. 在邮件管理页面点击"创建邮件"
2. 填写邮件信息和奖励
3. 系统会自动发送给所有玩家

### 奖励设置
- **基础资源**：直接输入数量
- **物品奖励**：填写物品名称、数量和类型
- 可以添加多个物品奖励

## 注意事项

1. **权限控制**：当前版本没有登录验证，生产环境请添加身份验证
2. **数据备份**：删除操作不可撤销，请谨慎操作
3. **性能考虑**：大量玩家时建议分批发送邮件
4. **错误处理**：如遇到错误，请检查控制台日志

## 技术栈

- **后端**：Node.js + Express
- **数据库**：腾讯云数据库
- **前端**：Bootstrap 5 + EJS模板
- **云服务**：微信小程序云开发

## 开发和扩展

### 添加新功能
1. 在 `server.js` 中添加新的路由
2. 在 `views` 目录下创建对应的页面模板
3. 更新侧边栏导航

### 自定义样式
编辑各页面模板中的 `<style>` 标签或创建独立的CSS文件

### 数据库操作
使用微信云开发SDK进行数据库操作，参考现有代码示例

## 故障排除

### 常见问题
1. **连接数据库失败**：检查云开发环境ID是否正确
2. **邮件发送失败**：检查玩家数据和奖励格式
3. **页面加载慢**：考虑添加分页和缓存

### 日志查看
服务器日志会输出到控制台，包含详细的错误信息

## 更新日志

### v1.0.0
- 初始版本
- 基础的玩家管理功能
- 完整的邮件系统
- 系统概览页面
