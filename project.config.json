{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "__usePrivacyCheck__": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "cloud": true}, "compileType": "game", "libVersion": "2.33.0", "appid": "wx38dbd9a27dc9fefb", "projectname": "修仙六道", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "cloudbaseRoot": "", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloud": true, "miniprogramRoot": "", "srcMiniprogramRoot": "", "cloudbaseInfo": {"containerDebugEnvs": [], "cloudbaseEnvironmentId": "cloud1-9gzbxxbff827656f"}}