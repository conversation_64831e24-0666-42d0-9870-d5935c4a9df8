<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-left: 4px solid #007bff;
        }
        .stats-card.success {
            border-left-color: #28a745;
        }
        .stats-card.warning {
            border-left-color: #ffc107;
        }
        .stats-card.danger {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">管理系统</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/">
                                <i class="bi bi-house-door"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/players">
                                <i class="bi bi-people"></i>
                                玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/mails">
                                <i class="bi bi-envelope"></i>
                                邮件管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统概览</h1>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总玩家数
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalPlayers">
                                            加载中...
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card success h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            今日活跃
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayActive">
                                            加载中...
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-graph-up text-success" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card warning h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            未读邮件
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="unreadMails">
                                            加载中...
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-envelope text-warning" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card danger h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            系统状态
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            正常
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="/players" class="btn btn-primary">
                                        <i class="bi bi-people"></i>
                                        管理玩家
                                    </a>
                                    <a href="/mails" class="btn btn-success">
                                        <i class="bi bi-envelope-plus"></i>
                                        发送邮件
                                    </a>
                                    <button class="btn btn-info" onclick="refreshStats()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        刷新统计
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">系统信息</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>系统版本:</strong> 1.0.0</li>
                                    <li><strong>数据库:</strong> 腾讯云数据库</li>
                                    <li><strong>服务器时间:</strong> <span id="serverTime"></span></li>
                                    <li><strong>运行状态:</strong> <span class="badge bg-success">正常</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载统计数据
        async function loadStats() {
            try {
                // 获取玩家总数
                const playersResponse = await fetch('/api/players?limit=1');
                const playersData = await playersResponse.json();
                if (playersData.success) {
                    document.getElementById('totalPlayers').textContent = playersData.total;
                }

                // 获取邮件统计
                const mailsResponse = await fetch('/api/mails?limit=1');
                const mailsData = await mailsResponse.json();
                if (mailsData.success) {
                    document.getElementById('unreadMails').textContent = mailsData.total;
                }

                // 今日活跃玩家（简化处理，显示总数的一半）
                document.getElementById('todayActive').textContent = Math.floor(playersData.total / 2);

            } catch (error) {
                console.error('加载统计数据失败:', error);
                document.getElementById('totalPlayers').textContent = '加载失败';
                document.getElementById('todayActive').textContent = '加载失败';
                document.getElementById('unreadMails').textContent = '加载失败';
            }
        }

        // 刷新统计
        function refreshStats() {
            loadStats();
        }

        // 更新服务器时间
        function updateServerTime() {
            document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            updateServerTime();
            
            // 每分钟更新一次时间
            setInterval(updateServerTime, 60000);
        });
    </script>
</body>
</html>
