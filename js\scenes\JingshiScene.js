/**
 * 静室场景类
 * 玩家主要角色的修炼场所
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import REALM_CONFIG from '../config/RealmConfig.js';

class JingshiScene extends BaseScene {
  // 静态变量，记录修炼是否已开始
  static meditationStarted = false;

  // 静态变量，记录上次运转时间
  static lastRotationTime = 0;

  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 圆形进度条旋转角度
    this.rotationAngle = 0;

    // 运转时间间隔(毫秒)
    this.rotationInterval = 6000; // 6秒

    // 是否正在运转
    this.isRotating = true;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到洞府场景
        this.sceneManager.showScene('dongfu');
      }
    );

    this.addUIElement(this.backButton);

    // 创建突破按钮
    this.createBreakthroughButton();

    // 创建灵力丹主按钮
    this.createLingliDanMainButton();

    // 确保自动修炼已启动
    if (!JingshiScene.meditationStarted) {
      JingshiScene.meditationStarted = true;
      JingshiScene.lastRotationTime = Date.now();

      // 如果是第一次进入，记录初始状态
      const player = game.gameStateManager.getPlayer();
      if (!player.jingshiFirstEntry) {
        player.jingshiFirstEntry = Date.now();
        game.gameStateManager.setPlayer(player);
      }

      console.log('自动修炼已启动');
    } else {
      // 如果已经启动，检查lastRotationTime是否有效
      const now = Date.now();
      if (!JingshiScene.lastRotationTime || JingshiScene.lastRotationTime > now) {
        JingshiScene.lastRotationTime = now;
        console.log('重置修炼时间');
      }
    }
  }

  // 创建灵力丹主按钮
  createLingliDanMainButton() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 10;

    // 创建主按钮，点击后显示灵力丹列表
    const mainButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      margin,
      buttonWidth,
      buttonHeight,
      '灵力丹',
      '#4CAF50',  // 绿色背景
      '#FFFFFF',  // 白色文字
      () => {
        // 切换灵力丹列表显示状态
        this.showLingliDanList = !this.showLingliDanList;

        // 清除点击区域
        if (this.clickAreas) {
          this.clickAreas = [];
        }

        // 如果显示灵力丹列表，创建灵力丹按钮
        if (this.showLingliDanList) {
          // 记录灵力丹区域信息，用于检测点击是否在区域内
          this.lingliDanAreaInfo = {
            x: this.screenWidth * 0.1,
            y: this.screenHeight * 0.8,
            width: this.screenWidth * 0.8,
            height: this.screenHeight * 0.15
          };

          console.log('显示灵力丹列表');
        } else {
          // 清除灵力丹区域信息
          this.lingliDanAreaInfo = null;
          console.log('隐藏灵力丹列表');
        }
      }
    );

    this.addUIElement(mainButton);
  }

  // 清除灵力丹按钮，但保留主按钮和返回按钮
  clearLingliDanButtons() {
    // 过滤出需要保留的按钮
    const buttonsToKeep = this.uiElements.filter(element => {
      // 保留返回按钮和灵力丹主按钮
      return element === this.backButton || element.text === '灵力丹' || element.text === '突破';
    });

    // 清空当前UI元素
    this.clearUIElements();

    // 重新添加需要保留的按钮
    buttonsToKeep.forEach(button => {
      this.addUIElement(button);
    });
  }

  // 获取可用的灵力丹列表
  getAvailableLingliDans() {
    // 获取玩家背包中的灵力丹
    const items = game.gameStateManager.getItems();

    // 筛选出所有灵力丹物品
    const lingliDanItems = items.filter(item => {
      // 首先确保 item 存在
      if (!item) return false;

      // 检查 id 是否包含 linglidan_
      const idMatch = item.id && typeof item.id === 'string' && item.id.includes('linglidan_');

      // 检查名称是否包含灵力丹
      const nameMatch = item.name && typeof item.name === 'string' && item.name.includes('灵力丹');

      // 检查描述是否包含灵力
      const descMatch = item.description && typeof item.description === 'string' && item.description.includes('灵力');

      return idMatch || nameMatch || descMatch;
    });

    // 过滤掉数量为0的灵力丹
    const availableDans = lingliDanItems.filter(item => item.count > 0);

    // 为每个灵力丹计算灵力加成值
    availableDans.forEach(item => {
      // 获取灵力加成值
      let lingliBonus = 0;

      // 先检查effects属性
      if (item.effects && item.effects.lingli) {
        lingliBonus = item.effects.lingli;
      }
      // 再检查effect属性
      else if (item.effect) {
        if (item.effect.type === 'lingli' && item.effect.value) {
          lingliBonus = item.effect.value;
        }
      }
      // 最后使用默认值
      else {
        // 默认灵力丹效果
        const defaultDans = {
          'linglidan_small': 100,
          'linglidan_medium': 500,
          'linglidan_large': 2000
        };
        lingliBonus = defaultDans[item.id] || 0;
      }

      // 如果还是没有获取到灵力加成，根据名称猜测
      if (lingliBonus <= 0) {
        if (item.name.includes('小灵力丹')) {
          lingliBonus = 100;
        } else if (item.name.includes('中灵力丹')) {
          lingliBonus = 500;
        } else if (item.name.includes('大灵力丹')) {
          lingliBonus = 2000;
        }
      }

      // 将灵力加成值保存到物品中
      item.lingliBonus = lingliBonus;
    });

    // 按灵力加成值排序（从大到小）
    return availableDans.sort((a, b) => b.lingliBonus - a.lingliBonus);
  }

  // 绘制灵力丹列表
  drawLingliDanList() {
    // 获取可用的灵力丹
    const availableDans = this.getAvailableLingliDans();

    // 如果没有灵力丹，显示提示
    if (availableDans.length === 0) {
      console.log('没有找到灵力丹物品');

      // 绘制灵力丹区域背景
      const areaX = this.screenWidth * 0.1;
      const areaY = this.screenHeight * 0.8;
      const areaWidth = this.screenWidth * 0.8;
      const areaHeight = this.screenHeight * 0.15;

      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 128, 0.1)';
      this.ctx.fillRect(areaX, areaY, areaWidth, areaHeight);

      // 绘制边框
      this.ctx.strokeStyle = 'rgba(0, 0, 128, 0.5)';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(areaX, areaY, areaWidth, areaHeight);

      // 绘制标题
      this.ctx.fillStyle = '#000080';
      this.ctx.font = '18px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('灵力丹列表', this.screenWidth / 2, areaY - 10);

      // 绘制提示文字
      this.ctx.fillStyle = '#FF6666';
      this.ctx.font = '18px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('没有可用的灵力丹', this.screenWidth / 2, areaY + areaHeight / 2);
      return;
    }

    // 绘制灵力丹区域背景
    const areaX = this.screenWidth * 0.1;
    const areaY = this.screenHeight * 0.8;
    const areaWidth = this.screenWidth * 0.8;
    const areaHeight = this.screenHeight * 0.15;

    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 128, 0.1)';
    this.ctx.fillRect(areaX, areaY, areaWidth, areaHeight);

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(0, 0, 128, 0.5)';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(areaX, areaY, areaWidth, areaHeight);

    // 绘制标题
    this.ctx.fillStyle = '#000080';
    this.ctx.font = '18px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('灵力丹列表', this.screenWidth / 2, areaY - 10);

    // 计算每个灵力丹图标的大小和间距
    const iconSize = 50;
    const iconMargin = 10;
    const maxIconsPerRow = Math.floor(areaWidth / (iconSize + iconMargin));
    const startX = areaX + (areaWidth - Math.min(availableDans.length, maxIconsPerRow) * (iconSize + iconMargin)) / 2 + iconMargin / 2;

    // 清除点击区域
    this.clickAreas = [];

    // 绘制灵力丹图标和文字
    availableDans.forEach((item, index) => {
      const row = Math.floor(index / maxIconsPerRow);
      const col = index % maxIconsPerRow;
      const x = startX + col * (iconSize + iconMargin);
      const y = areaY + iconMargin + row * (iconSize + 30); // 30是文字的高度

      // 绘制图标背景
      this.ctx.fillStyle = '#4CAF50';
      this.ctx.fillRect(x, y, iconSize, iconSize);

      // 绘制图标边框
      this.ctx.strokeStyle = '#FFFFFF';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(x, y, iconSize, iconSize);

      // 绘制灵力丹名称
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = '14px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(item.name, x + iconSize / 2, y + iconSize / 2);

      // 绘制灵力加成值
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = '12px Arial';
      this.ctx.fillText(`+${item.lingliBonus}灵力`, x + iconSize / 2, y + iconSize + 15);

      // 绘制数量
      this.ctx.fillStyle = '#FFFF00';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`x${item.count}`, x + iconSize - 5, y + 15);

      // 创建点击区域
      const clickArea = {
        x: x,
        y: y,
        width: iconSize,
        height: iconSize,
        itemId: item.id,
        onClick: () => {
          this.useLingliDan(item.id);
        }
      };

      // 添加到点击区域列表
      this.clickAreas.push(clickArea);
    });
  }

  // 创建灵力丹按钮
  createLingliDanButtons() {
    // 这个方法现在只是为了兼容性，实际上不再使用
    console.log('创建灵力丹按钮方法已弃用，请使用drawLingliDanList方法');
  }

  // 创建突破按钮
  createBreakthroughButton() {
    const buttonWidth = 120;
    const buttonHeight = 50;

    // 检查是否可以突破
    const canBreakthrough = this.canBreakthrough();

    if (canBreakthrough) {
      this.breakthroughButton = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        this.screenHeight / 4 - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        '突破',
        null,
        null,
        () => {
          this.attemptBreakthrough();
        }
      );

      this.addUIElement(this.breakthroughButton);
    }
  }

  // 检查是否可以突破
  canBreakthrough() {
    const player = game.gameStateManager.getPlayer();
    const mainCharacter = this.getMainCharacter();

    if (!mainCharacter || !player) return false;

    // 获取当前灵力和突破所需灵力
    // 使用player.resources.lingli保持兼容性
    const currentLingLi = player.resources.lingli || 0;
    const requiredLingLi = this.getBreakthroughRequirement(mainCharacter.cultivation);

    // 检查是否在突破冷却期
    const now = Date.now();
    const cooldownEnd = player.breakthroughCooldown || 0;
    const inCooldown = now < cooldownEnd;

    // 如果在冷却期，显示剩余时间
    if (inCooldown) {
      const remainingTime = Math.ceil((cooldownEnd - now) / (60 * 60 * 1000));
      console.log(`突破冷却中，还需${remainingTime}小时`);
    }

    return currentLingLi >= requiredLingLi && !inCooldown;
  }

  // 获取主要角色
  getMainCharacter() {
    const characters = game.gameStateManager.getCharacters();
    if (!characters || characters.length === 0) return null;

    // 返回ID为1的角色(女剑仙)
    return characters.find(char => char.id === 1);
  }

  // 获取突破所需灵力
  getBreakthroughRequirement(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取下一个等级的配置
    const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

    // 如果找到下一个等级的配置，返回所需灵力，否则返回一个很大的值
    return nextLevelConfig ? nextLevelConfig.requiredLingLi : 999999;
  }

  // 根据境界名称获取等级
  getLevelFromCultivation(cultivation) {
    // 查找匹配的境界配置
    const realmConfig = REALM_CONFIG.find(config => config.name === cultivation);

    // 如果找到匹配的配置，返回等级，否则返回1
    return realmConfig ? realmConfig.level : 1;
  }

  // 使用灵力丹
  useLingliDan(danId) {
    // 获取玩家背包中的灵力丹
    const items = game.gameStateManager.getItems();
    const item = items.find(item => item && item.id === danId);

    if (!item || item.count <= 0) {
      wx.showToast({
        title: '没有该灵力丹',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log(`使用灵力丹: ${item.name}, ID: ${item.id}`);
    console.log('灵力丹数据:', JSON.stringify(item));

    // 获取灵力丹信息
    let lingliBonus = 0;

    // 先检查effects属性
    if (item.effects && item.effects.lingli) {
      lingliBonus = item.effects.lingli;
      console.log(`从 effects 获取灵力加成: ${lingliBonus}`);
    }
    // 再检查effect属性
    else if (item.effect) {
      if (item.effect.type === 'lingli' && item.effect.value) {
        lingliBonus = item.effect.value;
        console.log(`从 effect.value 获取灵力加成: ${lingliBonus}`);
      }
    }
    // 最后使用默认值
    else {
      // 默认灵力丹效果
      const defaultDans = {
        'linglidan_small': 100,
        'linglidan_medium': 500,
        'linglidan_large': 2000
      };
      lingliBonus = defaultDans[danId] || 0;
      console.log(`从默认值获取灵力加成: ${lingliBonus}`);
    }

    // 如果还是没有获取到灵力加成，根据名称猜测
    if (lingliBonus <= 0) {
      if (item.name.includes('小灵力丹')) {
        lingliBonus = 100;
      } else if (item.name.includes('中灵力丹')) {
        lingliBonus = 500;
      } else if (item.name.includes('大灵力丹')) {
        lingliBonus = 2000;
      }
      console.log(`从名称猜测灵力加成: ${lingliBonus}`);
    }

    if (lingliBonus <= 0) {
      wx.showToast({
        title: '灵力丹无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取主角色
    const mainCharacter = this.getMainCharacter();
    if (!mainCharacter) {
      wx.showToast({
        title: '无法找到主角色',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取玩家数据
    const player = game.gameStateManager.getPlayer();
    if (!player) {
      wx.showToast({
        title: '无法获取玩家数据',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 使用灵力丹增加灵力（不允许自动突破）
    mainCharacter.addLingli(lingliBonus, false);

    // 更新玩家灵力资源
    if (!player.resources) player.resources = {};
    player.resources.lingli = (player.resources.lingli || 0) + lingliBonus;

    // 更新角色和玩家数据
    game.gameStateManager.updateCharacter(mainCharacter.id, mainCharacter);
    game.gameStateManager.setPlayer(player);

    // 消耗灵力丹
    game.gameStateManager.removeItem(danId, 1);

    // 显示使用成功提示
    wx.showToast({
      title: `使用成功，增加${lingliBonus}灵力`,
      icon: 'success',
      duration: 2000
    });

    // 检查是否可以突破
    if (this.canBreakthrough()) {
      this.createBreakthroughButton();
    }

    // 重新绘制进度条，更新灵力显示
    this.drawProgressBar();

    // 如果正在显示灵力丹列表，重新绘制列表
    if (this.showLingliDanList) {
      this.drawLingliDanList();
    }
  }

  // 尝试突破
  attemptBreakthrough() {
    const player = game.gameStateManager.getPlayer();
    const mainCharacter = this.getMainCharacter();

    if (!mainCharacter) return;

    // 获取当前灵力和突破所需灵力
    const currentLingLi = player.resources.lingli || 0;
    const requiredLingLi = this.getBreakthroughRequirement(mainCharacter.cultivation);

    if (currentLingLi < requiredLingLi) {
      wx.showToast({
        title: '灵力不足',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(mainCharacter.cultivation);

    // 获取当前等级的配置
    const currentLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel);

    // 如果是大突破，跳转到突破页面
    if (currentLevelConfig && currentLevelConfig.breakthroughType === "big") {
      // 跳转到突破页面
      this.sceneManager.showScene('breakthrough', {
        characterId: mainCharacter.id,
        returnScene: 'jingshi'
      });
    } else {
      // 如果是小突破，直接执行突破
      this.performSmallBreakthrough(mainCharacter, player, requiredLingLi);
    }
  }

  // 执行小突破
  performSmallBreakthrough(character, player, requiredLingLi) {
    // 获取突破成功率
    const successRate = this.getBreakthroughSuccessRate(character.cultivation);

    // 随机决定是否突破成功
    const random = Math.random();
    if (random <= successRate) {
      // 突破成功
      const nextCultivation = this.getNextCultivation(character.cultivation);
      character.cultivation = nextCultivation;

      // 如果角色有lingli属性，将其设置为0（兼容旧版本）
      if (character.hasOwnProperty('lingli')) {
        character.lingli = 0;
      }

      // 重置角色的exp属性
      character.exp = 0;

      // 从REALM_CONFIG中获取下一个等级的配置
      const nextLevel = this.getLevelFromCultivation(nextCultivation);
      const nextLevelConfig = REALM_CONFIG.find(config => config.level === nextLevel);

      // 提升角色等级
      character.level = nextLevel;
      console.log(`角色 ${character.name} 等级提升到 ${character.level}`);

      // 更新角色属性
      if (nextLevelConfig && nextLevelConfig.baseAttributes) {
        character.attributes.hp = nextLevelConfig.baseAttributes.hp;
        character.attributes.attack = nextLevelConfig.baseAttributes.attack;
        character.attributes.defense = nextLevelConfig.baseAttributes.defense;
        character.attributes.speed = nextLevelConfig.baseAttributes.speed;
      }

      // 保存旧战力用于比较
      const oldPower = character.power || 0;

      // 重新计算战力
      const newPower = character.calculatePower ? character.calculatePower() : 0;

      // 更新角色
      game.gameStateManager.updateCharacter(character.id, character);

      // 扣除灵力
      player.resources.lingli -= requiredLingLi;

      // 重置突破加成
      player.breakthroughBonus = 0;

      game.gameStateManager.setPlayer(player);

      // 显示突破成功提示
      wx.showToast({
        title: '突破成功',
        icon: 'success',
        duration: 1000
      });

      // 延迟1秒后计算战力并显示战力增长动画
      setTimeout(() => {
        // 计算玩家战力
        game.calculatePlayerPower && game.calculatePlayerPower();

        // 如果角色战力增加，触发角色战力增长事件
        if (newPower > oldPower) {
          console.log(`角色战力增长: ${oldPower} -> ${newPower}, 增长: ${newPower - oldPower}`);

          // 触发角色战力增长事件
          if (game.eventSystem) {
            game.eventSystem.emit('characterPowerIncrease', {
              characterId: character.id,
              oldPower,
              newPower,
              increase: newPower - oldPower
            });
          }
        }
      }, 1000); // 延迟1秒，等待突破成功提示消失
    } else {
      // 突破失败
      // 增加突破成功率
      if (!player.breakthroughBonus) {
        player.breakthroughBonus = 0;
      }
      player.breakthroughBonus += 0.05; // 每次失败增加5%成功率

      // 最高加成30%
      if (player.breakthroughBonus > 0.3) {
        player.breakthroughBonus = 0.3;
      }

      // 不扣除灵力
      game.gameStateManager.setPlayer(player);

      // 显示失败提示
      wx.showToast({
        title: `突破失败，成功率+5%`,
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 获取突破成功率
  getBreakthroughSuccessRate(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取当前等级的配置
    const currentLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel);

    // 获取玩家的突破加成
    const player = game.gameStateManager.getPlayer();
    const extraBonus = player && player.breakthroughBonus ? player.breakthroughBonus : 0;

    // 如果找到当前等级的配置，返回成功率加上玩家的突破加成，否则返回默认值
    return currentLevelConfig ? Math.min(currentLevelConfig.successRate + extraBonus, 0.95) : 0.9;
  }

  // 获取下一个境界
  getNextCultivation(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取下一个等级的配置
    const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

    // 如果找到下一个等级的配置，返回境界名称，否则返回当前境界
    return nextLevelConfig ? nextLevelConfig.name : cultivation;
  }

  // 场景显示时的回调
  onShow(params = {}) {
    // 如果有参数，可以处理特定逻辑
    if (params.returnFromBreakthrough) {
      console.log('从突破场景返回');
    }

    // 初始化UI
    this.initUI();

    // 检查玩家是否可以突破
    const player = game.gameStateManager.getPlayer();
    if (player) {
      // 显示离线收益弹窗
      this.showOfflineGainPopup(player);

      // 检查突破按钮
      if (player.canBreakthrough) {
        // 重新检查是否可以突破（以防玩家已经突破或灵力不足）
        if (this.canBreakthrough()) {
          this.createBreakthroughButton();
        }

        // 重置标志
        player.canBreakthrough = false;
        game.gameStateManager.setPlayer(player);
      }
    }
  }

  // 显示离线收益弹窗
  showOfflineGainPopup(player) {
    // 检查是否有离线收益
    if (!player.offlineGain || player.offlineGain.lingliGained <= 0) {
      return;
    }

    // 获取离线收益数据
    const { hours, minutes, lingliGained } = player.offlineGain;

    // 创建弹窗内容
    let content = `修士闲置了${hours}小时${minutes}分钟\n`;
    content += `朝门修炼获得灵力: ${lingliGained}\n`;

    // 显示弹窗
    wx.showModal({
      title: '离线修炼收益',
      content: content,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        // 清除离线收益数据，防止重复显示
        player.offlineGain = null;
        game.gameStateManager.setPlayer(player);
        console.log('离线收益弹窗已关闭');
      }
    });
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    // 清空点击区域
    this.clickAreas = [];
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 如果正在显示灵力丹列表
    if (this.showLingliDanList && this.lingliDanAreaInfo) {
      // 检查点击是否在灵力丹区域内
      const area = this.lingliDanAreaInfo;
      const isInArea = x >= area.x && x <= area.x + area.width && y >= area.y && y <= area.y + area.height;

      // 如果点击在灵力丹区域内，检查是否点击了灵力丹
      if (isInArea) {
        if (this.clickAreas && this.clickAreas.length > 0) {
          for (const clickArea of this.clickAreas) {
            if (x >= clickArea.x && x <= clickArea.x + clickArea.width && y >= clickArea.y && y <= clickArea.y + clickArea.height) {
              // 执行点击回调
              if (clickArea.onClick) {
                clickArea.onClick();
                return true;
              }
              break;
            }
          }
        }
        return true; // 即使没有点击到灵力丹，也返回 true，表示已处理点击事件
      } else {
        // 如果点击在灵力丹区域外，隐藏灵力丹列表
        this.showLingliDanList = false;
        this.lingliDanAreaInfo = null;
        this.clickAreas = [];
        console.log('点击区域外，隐藏灵力丹列表');
        return true;
      }
    }

    // 如果没有显示灵力丹列表，或者已经处理了点击事件，返回 false
    return false;
  }

  // 更新场景
  updateScene() {
    // 自动修炼逻辑已移到 drawCircularProgress 方法中处理
    // 这里只需要确保修炼已启动
    if (!JingshiScene.meditationStarted) {
      JingshiScene.meditationStarted = true;
      JingshiScene.lastRotationTime = Date.now();
      console.log('自动修炼已启动');
    }

    // 定期保存游戏状态
    if (Date.now() % (60 * 1000) < 1000) { // 每分钟保存一次
      game.gameStateManager.saveGameState();
    }
  }

  // 添加灵力
  addLingLi() {
    const mainCharacter = this.getMainCharacter();
    if (!mainCharacter) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 获取洞府灵气数量
    let lingqiAmount = 0;

    // 使用洞府系统获取灵气数量
    if (game.dongfuSystem) {
      const dongfuInfo = game.dongfuSystem.getDongfuLevelInfo(player.dongfuLevel);
      lingqiAmount = dongfuInfo.lingqiAmount;
    } else {
      // 如果没有洞府系统，使用默认计算方法
      lingqiAmount = 20 * Math.pow(2, player.dongfuLevel - 1); // 1级洞府20点灵气，每升一级翻倍
    }

    // 计算灵气吸收率
    let absorptionRate = 0.5; // 默认练气期吸收率为50%

    if (game.dongfuSystem) {
      absorptionRate = game.dongfuSystem.getCultivationAbsorptionRate(mainCharacter.cultivation);
    } else {
      // 如果没有洞府系统，使用默认计算方法
      if (mainCharacter.cultivation.includes('练气期')) {
        const layer = parseInt(mainCharacter.cultivation.replace(/[^0-9]/g, '')) || 1;
        absorptionRate = 0.5 + (layer - 1) * 0.01; // 每层增加1%
      } else if (mainCharacter.cultivation === '筑基期') {
        absorptionRate = 0.7;
      } else if (mainCharacter.cultivation === '金丹期') {
        absorptionRate = 0.75;
      } else if (mainCharacter.cultivation === '元婴期') {
        absorptionRate = 0.8;
      } else if (mainCharacter.cultivation === '化神期') {
        absorptionRate = 0.85;
      } else if (mainCharacter.cultivation === '炼虚期') {
        absorptionRate = 0.9;
      } else if (mainCharacter.cultivation === '合体期') {
        absorptionRate = 0.95;
      } else if (mainCharacter.cultivation === '大乘期') {
        absorptionRate = 1.0;
      } else if (mainCharacter.cultivation === '渡劫期') {
        absorptionRate = 1.05;
      }
    }

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(player.vipLevel);

    // 计算最终增加的灵力
    const addedLingLi = Math.floor(lingqiAmount * absorptionRate * (1 + vipBonus));

    // 更新角色经验值（不允许自动突破）
    mainCharacter.addLingli(addedLingLi, false);
    game.gameStateManager.updateCharacter(mainCharacter.id, mainCharacter);

    // 更新玩家灵力资源
    if (!player.resources) player.resources = {};
    player.resources.lingli = (player.resources.lingli || 0) + addedLingLi;

    // 更新玩家数据
    game.gameStateManager.setPlayer(player);

    console.log(`修炼完成，增加灵力: ${addedLingLi} (洞府灵气: ${lingqiAmount}, 吸收率: ${(absorptionRate * 100).toFixed(0)}%, VIP加成: ${(vipBonus * 100).toFixed(0)}%)`);

    // 检查是否可以突破，如果可以则创建突破按钮
    if (this.canBreakthrough()) {
      // 先移除现有的突破按钮（如果有）
      if (this.breakthroughButton) {
        this.removeUIElement(this.breakthroughButton);
      }

      // 创建新的突破按钮
      this.createBreakthroughButton();
    }

    // 重新绘制进度条，更新灵力显示
    this.drawProgressBar();
  }

  // 获取洞府加成
  getDongfuBonus(dongfuLevel) {
    // 每级洞府增加3%修炼速度
    return (dongfuLevel || 1) * 0.03;
  }

  // 获取VIP加成
  getVIPBonus(vipLevel) {
    if (!vipLevel || vipLevel <= 0) return 0;

    // 获取VIP系统
    if (!game.vipSystem) return 0;

    // 获取VIP等级信息
    const vipInfo = game.vipSystem.getVIPLevelInfo(vipLevel);
    if (!vipInfo || !vipInfo.benefits) return 0;

    // 返回VIP洞府修炼速度加成
    return vipInfo.benefits.dongfuSpeedBonus || 0;
  }

  // 获取修炼境界加成
  getCultivationBonus(cultivation) {
    // 练气期各层的加成
    if (cultivation.includes('练气期')) {
      const layer = parseInt(cultivation.replace(/[^0-9]/g, '')) || 1;
      // 练气期一层为0.1，每层增加0.01
      return 0.1 + (layer - 1) * 0.01;
    }

    // 其他境界的加成
    const bonuses = {
      '筑基期': 0.25,   // 25%
      '金丹期': 0.35,   // 35%
      '元婴期': 0.45,   // 45%
      '化神期': 0.55,   // 55%
      '炼虚期': 0.65,   // 65%
      '合体期': 0.75,   // 75%
      '大乘期': 0.85,   // 85%
      '渡劫期': 0.95    // 95%
    };

    return bonuses[cultivation] || 0;
  }

  // 静态方法：根据境界名称获取等级
  static getLevelFromCultivation(cultivation) {
    // 查找匹配的境界配置
    const realmConfig = REALM_CONFIG.find(config => config.name === cultivation);

    // 如果找到匹配的配置，返回等级，否则返回1
    return realmConfig ? realmConfig.level : 1;
  }

  // 静态方法：检查并更新修炼进度
  static checkAndUpdateMeditation() {
    // 获取当前时间
    const now = Date.now();

    // 获取主角色
    const characters = game.gameStateManager.getCharacters();
    const mainCharacter = characters.find(char => char.id === 1);
    if (!mainCharacter) return;

    // 获取玩家数据
    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 确保修炼已启动
    if (!JingshiScene.meditationStarted) {
      JingshiScene.meditationStarted = true;
      JingshiScene.lastRotationTime = now - 6000; // 设置为6秒前，确保立即有一次修炼周期完成
      console.log('自动修炼已启动（静态方法中）');
    }

    // 获取上次离线时间
    const lastOfflineTime = player.lastOfflineTime || JingshiScene.lastRotationTime || now;

    // 计算离线时间（毫秒）
    const offlineTime = now - lastOfflineTime;

    // 如果离线时间小于1秒，不计算离线收益
    if (offlineTime < 1000) {
      return;
    }

    // 计算修炼周期数（每6秒一个周期）
    const rotationInterval = 6000; // 要和实例中的值保持一致
    const completeCycles = Math.floor(offlineTime / rotationInterval);

    if (completeCycles > 0) {
      // 获取洞府灵气数量
      let lingqiAmount = 0;

      // 使用洞府系统获取灵气数量
      if (game.dongfuSystem) {
        const dongfuInfo = game.dongfuSystem.getDongfuLevelInfo(player.dongfuLevel);
        lingqiAmount = dongfuInfo.lingqiAmount;
      } else {
        // 如果没有洞府系统，使用默认计算方法
        lingqiAmount = 20 * Math.pow(2, player.dongfuLevel - 1); // 1级洞府20点灵气，每升一级翻倍
      }

      // 计算灵气吸收率
      let absorptionRate = 0.5; // 默认练气期吸收率为50%

      if (game.dongfuSystem) {
        absorptionRate = game.dongfuSystem.getCultivationAbsorptionRate(mainCharacter.cultivation);
      } else {
        // 如果没有洞府系统，使用默认计算方法
        if (mainCharacter.cultivation.includes('练气期')) {
          const layer = parseInt(mainCharacter.cultivation.replace(/[^0-9]/g, '')) || 1;
          absorptionRate = 0.5 + (layer - 1) * 0.01; // 每层增加1%
        } else if (mainCharacter.cultivation === '筑基期') {
          absorptionRate = 0.7;
        } else if (mainCharacter.cultivation === '金丹期') {
          absorptionRate = 0.75;
        } else if (mainCharacter.cultivation === '元婴期') {
          absorptionRate = 0.8;
        } else if (mainCharacter.cultivation === '化神期') {
          absorptionRate = 0.85;
        } else if (mainCharacter.cultivation === '炼虚期') {
          absorptionRate = 0.9;
        } else if (mainCharacter.cultivation === '合体期') {
          absorptionRate = 0.95;
        } else if (mainCharacter.cultivation === '大乘期') {
          absorptionRate = 1.0;
        } else if (mainCharacter.cultivation === '渡劫期') {
          absorptionRate = 1.05;
        }
      }

      // 获取VIP加成
      let vipBonus = 0;
      if (player.vipLevel > 0 && game.vipSystem) {
        const vipInfo = game.vipSystem.getVIPLevelInfo(player.vipLevel);
        if (vipInfo && vipInfo.benefits) {
          vipBonus = vipInfo.benefits.dongfuSpeedBonus || 0;
        }
      }

      // 计算最终增加的灵力
      const addedLingLi = Math.floor(lingqiAmount * absorptionRate * (1 + vipBonus) * completeCycles);

      // 更新角色经验值（不允许自动突破）
      mainCharacter.addLingli(addedLingLi, false);
      game.gameStateManager.updateCharacter(mainCharacter.id, mainCharacter);

      // 更新灵力
      if (!player.resources) player.resources = {};
      player.resources.lingli = (player.resources.lingli || 0) + addedLingLi;

      // 更新玩家数据
      player.lastOfflineTime = now;

      // 如果离线时间超过1分钟，记录离线收益
      if (offlineTime >= 60 * 1000) {
        // 计算离线小时数（向下取整）
        const offlineHours = Math.floor(offlineTime / (60 * 60 * 1000));
        const offlineMinutes = Math.floor((offlineTime % (60 * 60 * 1000)) / (60 * 1000));

        player.offlineGain = {
          hours: offlineHours,
          minutes: offlineMinutes,
          lingliGained: addedLingLi
        };
        console.log(`离线修炼完成，离线${offlineHours}小时${offlineMinutes}分钟，增加灵力: ${addedLingLi}`);
      } else {
        console.log(`修炼完成，增加灵力: ${addedLingLi} (周期数: ${completeCycles})`);
      }

      // 检查是否可以突破
      // 获取当前灵力和突破所需灵力
      const currentLingLi = player.resources.lingli || 0;

      // 使用REALM_CONFIG获取突破所需灵力
      // 从REALM_CONFIG中获取当前境界对应的等级
      const currentLevel = JingshiScene.getLevelFromCultivation(mainCharacter.cultivation);

      // 获取下一个等级的配置
      const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

      // 如果找到下一个等级的配置，返回所需灵力，否则返回一个很大的值
      const requiredLingLi = nextLevelConfig ? nextLevelConfig.requiredLingLi : 999999;

      console.log(`检查突破条件 - 当前灵力: ${currentLingLi}, 所需灵力: ${requiredLingLi}, 境界: ${mainCharacter.cultivation}`);

      // 如果灵力足够，设置标志以便下次进入静室时显示突破按钮
      if (currentLingLi >= requiredLingLi) {
        player.canBreakthrough = true;

        // 如果当前正在静室场景，立即显示突破按钮
        const currentScene = game.sceneManager.getCurrentScene();
        if (currentScene && currentScene.constructor.name === 'JingshiScene') {
          console.log('当前在静室场景，立即显示突破按钮');

          // 移除现有的突破按钮（如果有）
          if (currentScene.breakthroughButton) {
            currentScene.removeUIElement(currentScene.breakthroughButton);
          }

          // 创建新的突破按钮
          currentScene.createBreakthroughButton();

          // 强制重绘场景
          currentScene.drawScene();
        }
      }

      // 保存更新后的玩家数据
      game.gameStateManager.setPlayer(player);
    }

    // 更新最后修炼时间，保留未完成的部分
    JingshiScene.lastRotationTime = now - (now % rotationInterval);
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制修炼信息
    this.drawCultivationInfo();

    // 绘制进度条
    this.drawProgressBar();

    // 绘制圆形进度条
    this.drawCircularProgress();

    // 如果显示灵力丹列表，绘制灵力丹列表
    if (this.showLingliDanList) {
      this.drawLingliDanList();
    }
  }

  // 绘制背景
  drawBackground() {
    // 蓝白色的禁止图像背景
    this.ctx.fillStyle = '#f0f8ff'; // 蓝白色背景
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制禁止图案
    const gridSize = 30;
    this.ctx.strokeStyle = 'rgba(0, 0, 128, 0.1)'; // 淡蓝色线条
    this.ctx.lineWidth = 1;

    // 绘制水平线
    for (let y = 0; y <= this.screenHeight; y += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(this.screenWidth, y);
      this.ctx.stroke();
    }

    // 绘制垂直线
    for (let x = 0; x <= this.screenWidth; x += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }

    // 绘制中间的圆形静室区域
    const centerX = this.screenWidth / 2;
    const centerY = this.screenHeight / 2;
    const radius = Math.min(this.screenWidth, this.screenHeight) * 0.3;

    // 绘制淡蓝色圆形区域
    this.ctx.fillStyle = 'rgba(135, 206, 250, 0.3)';
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(0, 0, 128, 0.5)';
    this.ctx.lineWidth = 3;
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    this.ctx.stroke();
  }

  // 绘制修炼信息
  drawCultivationInfo() {
    const mainCharacter = this.getMainCharacter();
    if (!mainCharacter) return;

    // 绘制标题
    this.ctx.fillStyle = '#FFD700'; // 金色
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('修炼境界', this.screenWidth / 2, 80);

    // 绘制当前境界
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 30px Arial';
    this.ctx.fillText(mainCharacter.cultivation, this.screenWidth / 2, 130);

    // 绘制角色名称
    this.ctx.font = '20px Arial';
    this.ctx.fillText(mainCharacter.name, this.screenWidth / 2, 170);

    // 检查是否在突破冷却期
    const player = game.gameStateManager.getPlayer();
    if (player && player.breakthroughCooldown) {
      const now = Date.now();
      const cooldownEnd = player.breakthroughCooldown;

      if (now < cooldownEnd) {
        // 计算剩余时间
        const remainingMs = cooldownEnd - now;
        const remainingHours = Math.floor(remainingMs / (60 * 60 * 1000));
        const remainingMinutes = Math.floor((remainingMs % (60 * 60 * 1000)) / (60 * 1000));

        // 绘制冷却时间
        this.ctx.fillStyle = '#FF6666';
        this.ctx.font = '18px Arial';
        this.ctx.fillText(`突破冷却中: ${remainingHours}小时${remainingMinutes}分钟`, this.screenWidth / 2, 200);
      }
    }
  }

  // 绘制进度条
  drawProgressBar() {
    const player = game.gameStateManager.getPlayer();
    const mainCharacter = this.getMainCharacter();

    if (!mainCharacter || !player) return;

    // 获取当前灵力和突破所需灵力
    const currentLingLi = player.resources.lingli || 0;
    const requiredLingLi = this.getBreakthroughRequirement(mainCharacter.cultivation);

    // 进度条尺寸和位置
    const barWidth = this.screenWidth * 0.8;
    const barHeight = 30;
    const barX = (this.screenWidth - barWidth) / 2;
    const barY = this.screenHeight / 3;

    // 绘制进度条背景
    this.ctx.fillStyle = '#333333';
    this.ctx.fillRect(barX, barY, barWidth, barHeight);

    // 计算进度
    const progress = Math.min(1, currentLingLi / requiredLingLi);

    // 绘制进度
    this.ctx.fillStyle = '#4CAF50';
    this.ctx.fillRect(barX, barY, barWidth * progress, barHeight);

    // 绘制进度文本
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${currentLingLi}/${requiredLingLi}`, this.screenWidth / 2, barY + barHeight / 2 + 5);

    // 绘制灵力文本
    this.ctx.fillText('灵力', this.screenWidth / 2, barY - 10);
  }

  // 绘制圆形进度条
  drawCircularProgress() {
    // 圆形进度条位置和尺寸
    const centerX = this.screenWidth / 2;
    const centerY = this.screenHeight * 0.7;
    const radius = 50;

    // 绘制外圈
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    this.ctx.strokeStyle = '#0000AA';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // 绘制内圈
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius - 5, 0, Math.PI * 2);
    this.ctx.strokeStyle = '#0088FF';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // 获取当前时间
    const now = Date.now();
    const rotationInterval = this.rotationInterval; // 6000ms = 6秒

    // 确保修炼已启动
    if (!JingshiScene.meditationStarted) {
      JingshiScene.meditationStarted = true;
      JingshiScene.lastRotationTime = now - 1000; // 设置为1秒前，确保有平滑的动画
      console.log('自动修炼已启动（圆形进度条中）');
    }

    // 确保 lastRotationTime 有效
    if (!JingshiScene.lastRotationTime || JingshiScene.lastRotationTime > now) {
      JingshiScene.lastRotationTime = now - 1000; // 设置为1秒前，确保有平滑的动画
    }

    // 计算经过的时间和旋转角度
    const elapsed = now - JingshiScene.lastRotationTime;
    const cycleProgress = (elapsed % rotationInterval) / rotationInterval;
    this.rotationAngle = cycleProgress * Math.PI * 2;

    // 绘制旋转的指针
    this.ctx.save();
    this.ctx.translate(centerX, centerY);
    this.ctx.rotate(this.rotationAngle);

    this.ctx.beginPath();
    this.ctx.moveTo(0, 0);
    this.ctx.lineTo(0, -radius + 10);
    this.ctx.strokeStyle = '#000080';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    this.ctx.restore();

    // 计算剩余时间
    const remaining = rotationInterval - (elapsed % rotationInterval);
    const seconds = Math.max(1, Math.ceil(remaining / 1000)); // 确保至少显示1秒

    // 绘制剩余时间
    this.ctx.fillStyle = '#000080';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${seconds}秒`, centerX, centerY + radius + 30);

    // 绘制自动修炼状态
    this.ctx.fillStyle = '#000080';
    this.ctx.font = '18px Arial';
    this.ctx.fillText('自动修炼中', centerX, centerY - radius - 20);

    // 如果已经完成一个周期，更新最后旋转时间
    if (elapsed >= rotationInterval) {
      // 更新最后旋转时间，保留未完成的部分
      JingshiScene.lastRotationTime = now - (elapsed % rotationInterval);

      // 调用静态方法进行修炼结算
      JingshiScene.checkAndUpdateMeditation();

      // 重新绘制进度条，更新灵力显示
      this.drawProgressBar();
    }
  }
}

export default JingshiScene;