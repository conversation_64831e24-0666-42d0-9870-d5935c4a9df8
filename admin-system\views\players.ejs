<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            padding: 20px;
        }
        .player-card {
            transition: transform 0.2s;
        }
        .player-card:hover {
            transform: translateY(-2px);
        }
        .selected-player {
            border: 2px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">管理系统</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house-door"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/players">
                                <i class="bi bi-people"></i>
                                玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/mails">
                                <i class="bi bi-envelope"></i>
                                邮件管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">玩家管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="showSendMailModal()">
                            <i class="bi bi-envelope-plus"></i>
                            发送邮件
                        </button>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索玩家 (OpenID 或昵称)">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchPlayers()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="selectAllPlayers()">全选</button>
                            <button class="btn btn-outline-secondary" onclick="clearSelection()">清空选择</button>
                            <span class="badge bg-info align-self-center" id="selectedCount">已选择: 0</span>
                        </div>
                    </div>
                </div>

                <!-- 玩家列表 -->
                <div class="row" id="playersList">
                    <!-- 玩家卡片将在这里动态生成 -->
                </div>

                <!-- 分页 -->
                <nav aria-label="玩家列表分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 发送邮件模态框 -->
    <div class="modal fade" id="sendMailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发送邮件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="sendMailForm">
                        <div class="mb-3">
                            <label for="mailTitle" class="form-label">邮件标题</label>
                            <input type="text" class="form-control" id="mailTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="mailContent" class="form-label">邮件内容</label>
                            <textarea class="form-control" id="mailContent" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="mailSender" class="form-label">发送者</label>
                            <input type="text" class="form-control" id="mailSender" value="系统管理员">
                        </div>
                        
                        <!-- 奖励设置 -->
                        <div class="mb-3">
                            <h6>奖励设置</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="rewardXianyu" class="form-label">仙玉</label>
                                    <input type="number" class="form-control" id="rewardXianyu" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLingshi" class="form-label">灵石</label>
                                    <input type="number" class="form-control" id="rewardLingshi" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLianlidian" class="form-label">历练点</label>
                                    <input type="number" class="form-control" id="rewardLianlidian" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardSwordIntent" class="form-label">剑意</label>
                                    <input type="number" class="form-control" id="rewardSwordIntent" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- 物品奖励 -->
                        <div class="mb-3">
                            <h6>物品奖励</h6>
                            <div id="itemRewards">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-control" name="itemType">
                                            <option value="material">材料</option>
                                            <option value="consumable">消耗品</option>
                                            <option value="equipment">装备</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-success" onclick="addItemReward()">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">接收者 (<span id="recipientCount">0</span> 人)</label>
                            <div class="form-text">已选择的玩家将接收此邮件</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="sendMail()">发送邮件</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let players = [];
        let selectedPlayers = new Set();
        let currentPage = 1;
        let totalPages = 1;

        // 加载玩家列表
        async function loadPlayers(page = 1, search = '') {
            try {
                const response = await fetch(`/api/players?page=${page}&limit=12&search=${encodeURIComponent(search)}`);
                const data = await response.json();
                
                if (data.success) {
                    players = data.data;
                    currentPage = data.page;
                    totalPages = Math.ceil(data.total / data.limit);
                    
                    renderPlayers();
                    renderPagination();
                } else {
                    console.error('加载玩家列表失败:', data.error);
                }
            } catch (error) {
                console.error('加载玩家列表失败:', error);
            }
        }

        // 渲染玩家列表
        function renderPlayers() {
            const container = document.getElementById('playersList');
            container.innerHTML = '';

            players.forEach(player => {
                const isSelected = selectedPlayers.has(player._id);
                const card = document.createElement('div');
                card.className = 'col-md-4 mb-3';
                card.innerHTML = `
                    <div class="card player-card ${isSelected ? 'selected-player' : ''}" onclick="togglePlayerSelection('${player._id}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <h6 class="card-title">${player.nickname || '未设置昵称'}</h6>
                                <input type="checkbox" ${isSelected ? 'checked' : ''} onclick="event.stopPropagation()">
                            </div>
                            <p class="card-text">
                                <small class="text-muted">OpenID: ${player._openid.substring(0, 10)}...</small><br>
                                <small class="text-muted">服务器: ${player.server_name || '未知'}</small><br>
                                <small class="text-muted">VIP等级: ${player.vip_level || 0}</small><br>
                                <small class="text-muted">仙玉: ${player.xianyu || 0}</small>
                            </p>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            updateSelectedCount();
        }

        // 渲染分页
        function renderPagination() {
            const container = document.getElementById('pagination');
            container.innerHTML = '';

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${currentPage - 1})">上一页</a>`;
            container.appendChild(prevLi);

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${i})">${i}</a>`;
                container.appendChild(li);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${currentPage + 1})">下一页</a>`;
            container.appendChild(nextLi);
        }

        // 切换玩家选择状态
        function togglePlayerSelection(playerId) {
            if (selectedPlayers.has(playerId)) {
                selectedPlayers.delete(playerId);
            } else {
                selectedPlayers.add(playerId);
            }
            renderPlayers();
        }

        // 全选玩家
        function selectAllPlayers() {
            players.forEach(player => selectedPlayers.add(player._id));
            renderPlayers();
        }

        // 清空选择
        function clearSelection() {
            selectedPlayers.clear();
            renderPlayers();
        }

        // 更新选择计数
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = `已选择: ${selectedPlayers.size}`;
            document.getElementById('recipientCount').textContent = selectedPlayers.size;
        }

        // 搜索玩家
        function searchPlayers() {
            const search = document.getElementById('searchInput').value;
            loadPlayers(1, search);
        }

        // 显示发送邮件模态框
        function showSendMailModal() {
            if (selectedPlayers.size === 0) {
                alert('请先选择要发送邮件的玩家');
                return;
            }
            
            const modal = new bootstrap.Modal(document.getElementById('sendMailModal'));
            modal.show();
        }

        // 添加物品奖励行
        function addItemReward() {
            const container = document.getElementById('itemRewards');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2';
            newRow.innerHTML = `
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                </div>
                <div class="col-md-3">
                    <select class="form-control" name="itemType">
                        <option value="material">材料</option>
                        <option value="consumable">消耗品</option>
                        <option value="equipment">装备</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.parentElement.remove()">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
        }

        // 发送邮件
        async function sendMail() {
            const title = document.getElementById('mailTitle').value;
            const content = document.getElementById('mailContent').value;
            const sender = document.getElementById('mailSender').value;

            if (!title || !content) {
                alert('请填写邮件标题和内容');
                return;
            }

            if (selectedPlayers.size === 0) {
                alert('请选择接收邮件的玩家');
                return;
            }

            // 收集奖励信息
            const rewards = {
                xianyu: parseInt(document.getElementById('rewardXianyu').value) || 0,
                lingshi: parseInt(document.getElementById('rewardLingshi').value) || 0,
                lianlidian: parseInt(document.getElementById('rewardLianlidian').value) || 0,
                sword_intent: parseInt(document.getElementById('rewardSwordIntent').value) || 0,
                items: []
            };

            // 收集物品奖励
            const itemRows = document.querySelectorAll('#itemRewards .row');
            itemRows.forEach(row => {
                const name = row.querySelector('input[name="itemName"]').value;
                const count = parseInt(row.querySelector('input[name="itemCount"]').value) || 1;
                const type = row.querySelector('select[name="itemType"]').value;
                
                if (name) {
                    rewards.items.push({ name, count, type });
                }
            });

            // 准备接收者列表
            const recipients = players
                .filter(player => selectedPlayers.has(player._id))
                .map(player => ({
                    openid: player._openid,
                    nickname: player.nickname
                }));

            try {
                const response = await fetch('/api/send-mail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        recipients,
                        title,
                        content,
                        sender,
                        rewards
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('邮件发送成功！');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('sendMailModal'));
                    modal.hide();
                    
                    // 重置表单
                    document.getElementById('sendMailForm').reset();
                    clearSelection();
                } else {
                    alert('邮件发送失败: ' + result.error);
                }
            } catch (error) {
                console.error('发送邮件失败:', error);
                alert('邮件发送失败，请稍后重试');
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadPlayers();
            
            // 搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchPlayers();
                }
            });
        });
    </script>
</body>
</html>
