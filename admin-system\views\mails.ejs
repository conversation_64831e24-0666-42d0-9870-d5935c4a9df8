<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            padding: 20px;
        }
        .mail-item {
            transition: background-color 0.2s;
        }
        .mail-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">管理系统</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house-door"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/players">
                                <i class="bi bi-people"></i>
                                玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/mails">
                                <i class="bi bi-envelope"></i>
                                邮件管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">邮件管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="showCreateMailModal()">
                            <i class="bi bi-envelope-plus"></i>
                            创建邮件
                        </button>
                    </div>
                </div>

                <!-- 邮件列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">邮件列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>标题</th>
                                        <th>发送者</th>
                                        <th>是否有奖励</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="mailsList">
                                    <!-- 邮件列表将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="邮件列表分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将在这里动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建邮件模态框 -->
    <div class="modal fade" id="createMailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建全服邮件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createMailForm">
                        <div class="mb-3">
                            <label for="mailTitle" class="form-label">邮件标题</label>
                            <input type="text" class="form-control" id="mailTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="mailContent" class="form-label">邮件内容</label>
                            <textarea class="form-control" id="mailContent" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="mailSender" class="form-label">发送者</label>
                            <input type="text" class="form-control" id="mailSender" value="系统管理员">
                        </div>
                        
                        <!-- 奖励设置 -->
                        <div class="mb-3">
                            <h6>奖励设置</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="rewardXianyu" class="form-label">仙玉</label>
                                    <input type="number" class="form-control" id="rewardXianyu" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLingshi" class="form-label">灵石</label>
                                    <input type="number" class="form-control" id="rewardLingshi" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLianlidian" class="form-label">历练点</label>
                                    <input type="number" class="form-control" id="rewardLianlidian" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardSwordIntent" class="form-label">剑意</label>
                                    <input type="number" class="form-control" id="rewardSwordIntent" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- 物品奖励 -->
                        <div class="mb-3">
                            <h6>物品奖励</h6>
                            <div id="itemRewards">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-control" name="itemType">
                                            <option value="material">材料</option>
                                            <option value="consumable">消耗品</option>
                                            <option value="equipment">装备</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-success" onclick="addItemReward()">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            此邮件将发送给所有玩家
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createMail()">创建邮件</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 邮件详情模态框 -->
    <div class="modal fade" id="mailDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">邮件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="mailDetailContent">
                    <!-- 邮件详情内容将在这里显示 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="deleteMail()" id="deleteMailBtn">删除邮件</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let mails = [];
        let currentPage = 1;
        let totalPages = 1;
        let currentMailId = null;

        // 加载邮件列表
        async function loadMails(page = 1) {
            try {
                const response = await fetch(`/api/mails?page=${page}&limit=10`);
                const data = await response.json();
                
                if (data.success) {
                    mails = data.data;
                    currentPage = data.page;
                    totalPages = Math.ceil(data.total / data.limit);
                    
                    renderMails();
                    renderPagination();
                } else {
                    console.error('加载邮件列表失败:', data.error);
                }
            } catch (error) {
                console.error('加载邮件列表失败:', error);
            }
        }

        // 渲染邮件列表
        function renderMails() {
            const tbody = document.getElementById('mailsList');
            tbody.innerHTML = '';

            mails.forEach(mail => {
                const row = document.createElement('tr');
                row.className = 'mail-item';
                row.innerHTML = `
                    <td>
                        <strong>${mail.title}</strong>
                        <br>
                        <small class="text-muted">${mail.content.substring(0, 50)}${mail.content.length > 50 ? '...' : ''}</small>
                    </td>
                    <td>${mail.sender}</td>
                    <td>
                        ${mail.has_rewards ? 
                            '<span class="badge bg-success">有奖励</span>' : 
                            '<span class="badge bg-secondary">无奖励</span>'
                        }
                    </td>
                    <td>${new Date(mail.created_at).toLocaleString('zh-CN')}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="showMailDetail('${mail._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteMail('${mail._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染分页
        function renderPagination() {
            const container = document.getElementById('pagination');
            container.innerHTML = '';

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadMails(${currentPage - 1})">上一页</a>`;
            container.appendChild(prevLi);

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadMails(${i})">${i}</a>`;
                container.appendChild(li);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadMails(${currentPage + 1})">下一页</a>`;
            container.appendChild(nextLi);
        }

        // 显示创建邮件模态框
        function showCreateMailModal() {
            const modal = new bootstrap.Modal(document.getElementById('createMailModal'));
            modal.show();
        }

        // 显示邮件详情
        function showMailDetail(mailId) {
            const mail = mails.find(m => m._id === mailId);
            if (!mail) return;

            currentMailId = mailId;
            
            const content = document.getElementById('mailDetailContent');
            content.innerHTML = `
                <div class="mb-3">
                    <h6>标题</h6>
                    <p>${mail.title}</p>
                </div>
                <div class="mb-3">
                    <h6>发送者</h6>
                    <p>${mail.sender}</p>
                </div>
                <div class="mb-3">
                    <h6>内容</h6>
                    <p>${mail.content}</p>
                </div>
                <div class="mb-3">
                    <h6>创建时间</h6>
                    <p>${new Date(mail.created_at).toLocaleString('zh-CN')}</p>
                </div>
                ${mail.has_rewards ? `
                    <div class="mb-3">
                        <h6>奖励内容</h6>
                        <div class="alert alert-info">
                            ${mail.rewards.xianyu ? `仙玉: ${mail.rewards.xianyu}<br>` : ''}
                            ${mail.rewards.lingshi ? `灵石: ${mail.rewards.lingshi}<br>` : ''}
                            ${mail.rewards.lianlidian ? `历练点: ${mail.rewards.lianlidian}<br>` : ''}
                            ${mail.rewards.sword_intent ? `剑意: ${mail.rewards.sword_intent}<br>` : ''}
                            ${mail.rewards.items && mail.rewards.items.length > 0 ? 
                                mail.rewards.items.map(item => `${item.name} x${item.count}`).join('<br>') : ''
                            }
                        </div>
                    </div>
                ` : ''}
            `;

            const modal = new bootstrap.Modal(document.getElementById('mailDetailModal'));
            modal.show();
        }

        // 添加物品奖励行
        function addItemReward() {
            const container = document.getElementById('itemRewards');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2';
            newRow.innerHTML = `
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                </div>
                <div class="col-md-3">
                    <select class="form-control" name="itemType">
                        <option value="material">材料</option>
                        <option value="consumable">消耗品</option>
                        <option value="equipment">装备</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.parentElement.remove()">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
        }

        // 创建全服邮件
        async function createMail() {
            const title = document.getElementById('mailTitle').value;
            const content = document.getElementById('mailContent').value;
            const sender = document.getElementById('mailSender').value;

            if (!title || !content) {
                alert('请填写邮件标题和内容');
                return;
            }

            // 收集奖励信息
            const rewards = {
                xianyu: parseInt(document.getElementById('rewardXianyu').value) || 0,
                lingshi: parseInt(document.getElementById('rewardLingshi').value) || 0,
                lianlidian: parseInt(document.getElementById('rewardLianlidian').value) || 0,
                sword_intent: parseInt(document.getElementById('rewardSwordIntent').value) || 0,
                items: []
            };

            // 收集物品奖励
            const itemRows = document.querySelectorAll('#itemRewards .row');
            itemRows.forEach(row => {
                const name = row.querySelector('input[name="itemName"]').value;
                const count = parseInt(row.querySelector('input[name="itemCount"]').value) || 1;
                const type = row.querySelector('select[name="itemType"]').value;
                
                if (name) {
                    rewards.items.push({ name, count, type });
                }
            });

            try {
                // 首先获取所有玩家
                const playersResponse = await fetch('/api/players?limit=1000');
                const playersData = await playersResponse.json();
                
                if (!playersData.success) {
                    throw new Error('获取玩家列表失败');
                }

                const recipients = playersData.data.map(player => ({
                    openid: player._openid,
                    nickname: player.nickname
                }));

                const response = await fetch('/api/send-mail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        recipients,
                        title,
                        content,
                        sender,
                        rewards
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('全服邮件创建成功！');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createMailModal'));
                    modal.hide();
                    
                    // 重置表单
                    document.getElementById('createMailForm').reset();
                    loadMails();
                } else {
                    alert('邮件创建失败: ' + result.error);
                }
            } catch (error) {
                console.error('创建邮件失败:', error);
                alert('邮件创建失败，请稍后重试');
            }
        }

        // 确认删除邮件
        function confirmDeleteMail(mailId) {
            if (confirm('确定要删除这封邮件吗？此操作不可撤销。')) {
                deleteMailById(mailId);
            }
        }

        // 删除邮件
        async function deleteMail() {
            if (!currentMailId) return;
            
            if (confirm('确定要删除这封邮件吗？此操作不可撤销。')) {
                await deleteMailById(currentMailId);
                const modal = bootstrap.Modal.getInstance(document.getElementById('mailDetailModal'));
                modal.hide();
            }
        }

        // 根据ID删除邮件
        async function deleteMailById(mailId) {
            try {
                const response = await fetch(`/api/mails/${mailId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('邮件删除成功');
                    loadMails();
                } else {
                    alert('邮件删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除邮件失败:', error);
                alert('邮件删除失败，请稍后重试');
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadMails();
        });
    </script>
</body>
</html>
