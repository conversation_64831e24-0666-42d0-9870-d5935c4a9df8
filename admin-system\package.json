{"name": "xiuxian-admin-system", "version": "1.0.0", "description": "修仙游戏后台管理系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["admin", "game", "management"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "wx-server-sdk": "~2.6.3", "dotenv": "^16.3.1", "ejs": "^3.1.9"}, "devDependencies": {"nodemon": "^3.0.1"}}